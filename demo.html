<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>React 交互性：排队更新 & 不可变数据</title>
  <style>
    body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; padding: 20px; line-height: 1.5; }
    .card { border: 1px solid #ddd; border-radius: 12px; padding: 16px; margin-bottom: 18px; }
    .row { display:flex; gap:12px; align-items:center; flex-wrap:wrap; }
    code { background:#f6f8fa; padding: 2px 6px; border-radius: 6px; }
    .muted { color:#666; font-size: 14px; }
    button { padding:8px 12px; border-radius:10px; border:1px solid #ccc; background:#fff; cursor:pointer;}
    button:hover { background:#f7f7f7; }
  </style>
</head>
<body>
  <div id="root"></div>

  <!-- React 18 UMD + Babel -->
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel">
    const { useState, useEffect, useRef } = React;

    function BatchingDemo() {
      const [likes, setLikes] = useState(0);
      const [mode, setMode] = useState("naive"); // 'naive' | 'functional'

      useEffect(() => {
        console.log("%c[渲染后] likes =", "color:green", likes);
      }, [likes]);

      function handleClick() {
        console.log("%c[点击前] likes =", "color:#888", likes);
        if (mode === "naive") {
          // 基于同一张快照（本次渲染的 likes）
          setLikes(likes + 1);
          setLikes(likes + 1);
          console.log("%c[点击后(仍是这次快照)] likes =", "color:#888", likes);
        } else {
          // 函数式更新：每次基于“上一份结果”
          setLikes(l => l + 1);
          setLikes(l => l + 1);
          console.log("%c[点击后(仍是这次快照)] likes =", "color:#888", likes);
        }
      }

      function reset() { setLikes(0); }

      return (
        <div className="card">
          <h3>实验 A：状态更新“排队”与“快照”</h3>
          <div className="row">
            <label><input type="radio" name="mode" value="naive"
              checked={mode==="naive"} onChange={() => setMode("naive")} /> 普通写法</label>
            <label><input type="radio" name="mode" value="functional"
              checked={mode==="functional"} onChange={() => setMode("functional")} /> 函数式更新</label>
            <button onClick={handleClick}>点一次（执行两次 setLikes） → Likes: {likes}</button>
            <button onClick={reset}>重置</button>
          </div>
          <p className="muted">
            预期：普通写法点一次从 <code>0</code> 变 <code>1</code>；函数式更新点一次从 <code>0</code> 变 <code>2</code>。<br/>
            打开控制台观察日志顺序（点击前 / 点击后(快照) / 渲染后）。
          </p>
        </div>
      );
    }

    function ListDemo() {
      const [items, setItems] = useState(["A", "B"]);
      const renderCount = useRef(0);
      renderCount.current++;

      function addWrong() {
        console.log("%c[错误示例] 直接修改原数组", "color:#b30");
        items.push(String.fromCharCode(65 + items.length)); // 直接改原数组 ❌
        setItems(items); // 传回同一个引用，React 认为“没变”，不一定触发渲染
      }

      function addRight() {
        console.log("%c[正确示例] 新建数组拷贝追加", "color:#0a0");
        const next = [...items, String.fromCharCode(65 + items.length)];
        setItems(next); // 新引用 ✅ 会触发渲染
      }

      function reset() { setItems(["A","B"]); }

      return (
        <div className="card">
          <h3>实验 B：不可变数据（数组）</h3>
          <div className="row">
            <button onClick={addWrong}>错误写法：push + set同一引用</button>
            <button onClick={addRight}>正确写法：拷贝并追加</button>
            <button onClick={reset}>重置</button>
          </div>
          <p>当前数组： <code>[{items.join(", ")}]</code></p>
          <p className="muted">本组件渲染次数：{renderCount.current}</p>
          <p className="muted">
            点“错误写法”时，按钮点击后数组可能已被改动，但由于引用未变，<b>界面与渲染计数可能不更新</b>；<br/>
            点“正确写法”时，因引用改变，界面会更新且渲染次数增加。
          </p>
        </div>
      );
    }

    function App() {
      return (
        <>
          <h2>React 交互性：排队更新 & 不可变数据</h2>
          <BatchingDemo />
          <ListDemo />
        </>
      );
    }

    const root = ReactDOM.createRoot(document.getElementById("root"));
    root.render(<App />);
  </script>
</body>
</html>
