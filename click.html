<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        export default function ButtonDemo() {
  function handleClick() {
    alert('你按了门铃！');
  }

  return (
    <button onClick={handleClick}>
      点我
    </button>
  );
}
ReactDOM.render(<ButtonDemo />,
document.getElementById('root'))
    </script>
</body>
</html>